/**
 * Native Font Configuration for Arbeidskontrakt PDF Generator
 *
 * Uses React-PDF built-in fonts that work reliably without external dependencies.
 * These fonts are embedded in React-PDF and support multiple weights/styles.
 */

/**
 * No font registration needed - using built-in fonts
 * React-PDF includes these fonts by default:
 * - Helvetica (normal, bold, oblique, bold-oblique)
 * - Times-Roman (normal, bold, italic, bold-italic)
 * - Courier (normal, bold, oblique, bold-oblique)
 */
export const registerContractFonts = () => {
  // No registration needed for built-in fonts
  console.log('Using React-PDF built-in fonts for contract generation');
};

/**
 * Built-in font families and their available weights/styles
 */
export const nativeFonts = {
  helvetica: {
    family: 'Helvetica',
    weights: {
      normal: 'normal',
      bold: 'bold',
    },
    styles: {
      normal: 'normal',
      italic: 'oblique', // Helvetica uses 'oblique' instead of 'italic'
    }
  },
  times: {
    family: 'Times-Roman',
    weights: {
      normal: 'normal',
      bold: 'bold',
    },
    styles: {
      normal: 'normal',
      italic: 'italic',
    }
  },
  courier: {
    family: 'Courier',
    weights: {
      normal: 'normal',
      bold: 'bold',
    },
    styles: {
      normal: 'normal',
      italic: 'oblique',
    }
  }
} as const;

/**
 * Font weights mapping for design system compatibility
 * Maps semantic names to actual built-in font capabilities
 */
export const contractFontWeights = {
  light: 'normal',     // Built-in fonts don't have light, use normal
  normal: 'normal',    // Standard weight
  medium: 'normal',    // Built-in fonts don't have medium, use normal
  semiBold: 'bold',    // Built-in fonts don't have semiBold, use bold
  bold: 'bold',        // Bold weight
  extraBold: 'bold',   // Built-in fonts don't have extraBold, use bold
  black: 'bold',       // Built-in fonts don't have black, use bold
} as const;
