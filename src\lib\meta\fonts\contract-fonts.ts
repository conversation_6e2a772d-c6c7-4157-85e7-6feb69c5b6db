/**
 * Font Registration for Arbeidskontrakt PDF Generator
 * 
 * Registers Source Sans Pro font family with multiple weights
 * for professional contract document typography.
 */

import { Font } from '@react-pdf/renderer';

/**
 * Register Source Sans Pro font family with comprehensive weight support
 * This enables all the font weights defined in the design system
 */
export const registerContractFonts = () => {
  Font.register({
    family: 'Source Sans Pro',
    fonts: [
      // Light (300)
      {
        src: 'https://fonts.gstatic.com/s/sourcesanspro/v22/6xK3dSBYKcSV-LCoeQqfX1RYOo3qN67lqDY.woff2',
        fontWeight: 300,
        fontStyle: 'normal'
      },
      
      // Light Italic (300)
      {
        src: 'https://fonts.gstatic.com/s/sourcesanspro/v22/6xK1dSBYKcSV-LCoeQqfX1RYOo3qPK7lsDY.woff2',
        fontWeight: 300,
        fontStyle: 'italic'
      },
      
      // Regular (400)
      {
        src: 'https://fonts.gstatic.com/s/sourcesanspro/v22/6xK3dSBYKcSV-LCoeQqfX1RYOo3qOK7l.woff2',
        fontWeight: 400,
        fontStyle: 'normal'
      },
      
      // Regular Italic (400)
      {
        src: 'https://fonts.gstatic.com/s/sourcesanspro/v22/6xK1dSBYKcSV-LCoeQqfX1RYOo3qPK7l.woff2',
        fontWeight: 400,
        fontStyle: 'italic'
      },
      
      // Medium (500)
      {
        src: 'https://fonts.gstatic.com/s/sourcesanspro/v22/6xKydSBYKcSV-LCoeQqfX1RYOo3ig4vwmhdu.woff2',
        fontWeight: 500,
        fontStyle: 'normal'
      },
      
      // Semi Bold (600)
      {
        src: 'https://fonts.gstatic.com/s/sourcesanspro/v22/6xKydSBYKcSV-LCoeQqfX1RYOo3ig4vwlxdu.woff2',
        fontWeight: 600,
        fontStyle: 'normal'
      },
      
      // Semi Bold Italic (600)
      {
        src: 'https://fonts.gstatic.com/s/sourcesanspro/v22/6xKwdSBYKcSV-LCoeQqfX1RYOo3qPZYslxdu3cOWxy6f.woff2',
        fontWeight: 600,
        fontStyle: 'italic'
      },
      
      // Bold (700)
      {
        src: 'https://fonts.gstatic.com/s/sourcesanspro/v22/6xKydSBYKcSV-LCoeQqfX1RYOo3ig4vwkxdu.woff2',
        fontWeight: 700,
        fontStyle: 'normal'
      },
      
      // Bold Italic (700)
      {
        src: 'https://fonts.gstatic.com/s/sourcesanspro/v22/6xKwdSBYKcSV-LCoeQqfX1RYOo3qPZYslxdu3cOWxy6f.woff2',
        fontWeight: 700,
        fontStyle: 'italic'
      },
      
      // Extra Bold (800)
      {
        src: 'https://fonts.gstatic.com/s/sourcesanspro/v22/6xKydSBYKcSV-LCoeQqfX1RYOo3ig4vwjxdu.woff2',
        fontWeight: 800,
        fontStyle: 'normal'
      },
      
      // Black (900)
      {
        src: 'https://fonts.gstatic.com/s/sourcesanspro/v22/6xKydSBYKcSV-LCoeQqfX1RYOo3ig4vwixdu.woff2',
        fontWeight: 900,
        fontStyle: 'normal'
      }
    ]
  });
};

/**
 * Font weights mapping for design system compatibility
 * Maps semantic names to numeric values supported by Source Sans Pro
 */
export const sourceSansProWeights = {
  light: 300,
  normal: 400,
  medium: 500,
  semiBold: 600,
  bold: 700,
  extraBold: 800,
  black: 900,
} as const;

/**
 * Check if Source Sans Pro fonts are properly registered
 * Useful for debugging font loading issues
 */
export const validateFontRegistration = () => {
  console.log('Source Sans Pro fonts registered for arbeidskontrakt PDF generation');
  return true;
};
