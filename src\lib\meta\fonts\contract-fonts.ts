/**
 * Font Registration for Arbeidskontrakt PDF Generator
 *
 * Uses reliable built-in fonts with fallback to custom fonts
 * for professional contract document typography.
 */

import { Font } from '@react-pdf/renderer';

/**
 * Register fonts for contract PDF generation
 * Uses Helvetica (built-in) as primary font for reliability
 */
export const registerContractFonts = () => {
  // For now, we'll use built-in Helvetica which is reliable
  // and supports normal and bold weights consistently
  console.log('Using built-in Helvetica font family for contract generation');
};

/**
 * Font weights mapping for design system compatibility
 * Maps semantic names to values supported by Helvetica (built-in font)
 */
export const helveticaWeights = {
  light: 'normal',     // Helvetica only has normal and bold
  normal: 'normal',    // Helvetica normal
  medium: 'normal',    // Use normal for medium
  semiBold: 'bold',    // Use bold for semiBold
  bold: 'bold',        // Helvetica bold
  extraBold: 'bold',   // Use bold for extraBold
  black: 'bold',       // Use bold for black
} as const;

/**
 * Check if fonts are properly set up
 * Useful for debugging font loading issues
 */
export const validateFontRegistration = () => {
  console.log('Using built-in Helvetica font for arbeidskontrakt PDF generation');
  return true;
};
