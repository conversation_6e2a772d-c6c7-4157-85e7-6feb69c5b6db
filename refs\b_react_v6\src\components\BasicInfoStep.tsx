"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"
import { ArrowRight, User, Briefcase, DollarSign } from "lucide-react"
import type { FormData } from "@/types/contract"

interface BasicInfoStepProps {
  formData: FormData
  updateFormData: (data: Partial<FormData>) => void
  onNext: () => void
}

export default function BasicInfoStep({ formData, updateFormData, onNext }: BasicInfoStepProps) {
  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.employeeName.trim()) newErrors.employeeName = "Navn er påkrevd"
    if (!formData.employeeAddress.trim()) newErrors.employeeAddress = "Adresse er påkrevd"
    if (!formData.employeeBirthDate) newErrors.employeeBirthDate = "Fødselsdato er påkrevd"
    if (!formData.startDate) newErrors.startDate = "Startdato er påkrevd"
    if (!formData.position.trim()) newErrors.position = "Stillingstittel er påkrevd"
    if (!formData.accountNumber.trim()) newErrors.accountNumber = "Kontonummer er påkrevd"
    if (formData.isTemporary && !formData.temporaryEndDate)
      newErrors.temporaryEndDate = "Sluttdato er påkrevd for midlertidig ansettelse"
    if (formData.isTemporary && !formData.temporaryReason.trim())
      newErrors.temporaryReason = "Grunnlag er påkrevd for midlertidig ansettelse"
    if (formData.probationPeriod && (formData.probationMonths < 1 || formData.probationMonths > 6))
      newErrors.probationMonths = "Prøvetid må være mellom 1 og 6 måneder"

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    validateForm() // Vis advarsler, men ikke stopp brukeren
    onNext() // Gå videre uansett
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center text-base">
            <User className="h-5 w-5 mr-2 text-green-500" />
            Personopplysninger
          </CardTitle>
          <CardDescription>Grunnleggende informasjon om den ansatte</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 pt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="employeeName">Fullt navn *</Label>
              <Input
                id="employeeName"
                value={formData.employeeName}
                onChange={(e) => updateFormData({ employeeName: e.target.value })}
                placeholder="Ola Nordmann"
                className={errors.employeeName ? "border-red-500" : ""}
              />
              {errors.employeeName && <p className="text-red-500 text-sm mt-1">{errors.employeeName}</p>}
            </div>
            <div>
              <Label htmlFor="employeeBirthDate">Fødselsdato *</Label>
              <Input
                id="employeeBirthDate"
                type="date"
                value={formData.employeeBirthDate}
                onChange={(e) => updateFormData({ employeeBirthDate: e.target.value })}
                className={errors.employeeBirthDate ? "border-red-500" : ""}
              />
              {errors.employeeBirthDate && <p className="text-red-500 text-sm mt-1">{errors.employeeBirthDate}</p>}
            </div>
          </div>
          <div>
            <Label htmlFor="employeeAddress">Adresse *</Label>
            <Input
              id="employeeAddress"
              value={formData.employeeAddress}
              onChange={(e) => updateFormData({ employeeAddress: e.target.value })}
              placeholder="Gateadresse, postnummer og poststed"
              className={errors.employeeAddress ? "border-red-500" : ""}
            />
            {errors.employeeAddress && <p className="text-red-500 text-sm mt-1">{errors.employeeAddress}</p>}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center text-base">
            <Briefcase className="h-5 w-5 mr-2 text-green-500" />
            Stillingsinformasjon
          </CardTitle>
          <CardDescription>Informasjon om stillingen og ansettelsesforholdet</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 pt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="position">Stillingstittel *</Label>
              <Input
                id="position"
                value={formData.position}
                onChange={(e) => updateFormData({ position: e.target.value })}
                placeholder="Anleggsgartner"
                className={errors.position ? "border-red-500" : ""}
              />
              {errors.position && <p className="text-red-500 text-sm mt-1">{errors.position}</p>}
            </div>
            <div>
              <Label htmlFor="startDate">Startdato *</Label>
              <Input
                id="startDate"
                type="date"
                value={formData.startDate}
                onChange={(e) => updateFormData({ startDate: e.target.value })}
                className={errors.startDate ? "border-red-500" : ""}
              />
              {errors.startDate && <p className="text-red-500 text-sm mt-1">{errors.startDate}</p>}
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="isTemporary"
                checked={formData.isTemporary}
                onCheckedChange={(checked) =>
                  updateFormData({
                    isTemporary: checked as boolean,
                    // Clear temporary fields if unchecked
                    temporaryEndDate: checked ? formData.temporaryEndDate : "",
                    temporaryReason: checked ? formData.temporaryReason : "",
                  })
                }
              />
              <Label htmlFor="isTemporary">Midlertidig ansettelse</Label>
            </div>

            {formData.isTemporary && (
              <div className="ml-6 space-y-4 border-l-2 border-green-200 pl-4">
                <div>
                  <Label htmlFor="temporaryEndDate">Midlertidig til dato *</Label>
                  <Input
                    id="temporaryEndDate"
                    type="date"
                    value={formData.temporaryEndDate}
                    onChange={(e) => updateFormData({ temporaryEndDate: e.target.value })}
                    className={errors.temporaryEndDate ? "border-red-500" : ""}
                  />
                  {errors.temporaryEndDate && <p className="text-red-500 text-sm mt-1">{errors.temporaryEndDate}</p>}
                </div>
                <div>
                  <Label htmlFor="temporaryReason">Grunnlag for midlertidig ansettelse *</Label>
                  <Textarea
                    id="temporaryReason"
                    value={formData.temporaryReason}
                    onChange={(e) => updateFormData({ temporaryReason: e.target.value })}
                    placeholder="Sesongarbeid i anleggsperioden"
                    rows={3}
                    className={errors.temporaryReason ? "border-red-500" : ""}
                  />
                  {errors.temporaryReason && <p className="text-red-500 text-sm mt-1">{errors.temporaryReason}</p>}
                </div>
              </div>
            )}
          </div>

          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="probationPeriod"
                checked={formData.probationPeriod}
                onCheckedChange={(checked) =>
                  updateFormData({
                    probationPeriod: checked as boolean,
                    // Reset to default 6 months if unchecked
                    probationMonths: checked ? formData.probationMonths : 6,
                  })
                }
              />
              <Label htmlFor="probationPeriod">Prøvetid</Label>
            </div>

            {formData.probationPeriod && (
              <div className="ml-6 border-l-2 border-green-200 pl-4">
                <div>
                  <Label htmlFor="probationMonths">Antall måneder (maks 6) *</Label>
                  <Input
                    id="probationMonths"
                    type="number"
                    min="1"
                    max="6"
                    value={formData.probationMonths}
                    onChange={(e) => updateFormData({ probationMonths: Number(e.target.value) })}
                    className={errors.probationMonths ? "border-red-500" : ""}
                  />
                  {errors.probationMonths && <p className="text-red-500 text-sm mt-1">{errors.probationMonths}</p>}
                </div>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="ownTools"
              checked={formData.ownTools}
              onCheckedChange={(checked) => updateFormData({ ownTools: checked as boolean })}
            />
            <Label htmlFor="ownTools">Eget verktøy (kr 1,85/time)</Label>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center text-base">
            <DollarSign className="h-5 w-5 mr-2 text-green-500" />
            Lønn og konto
          </CardTitle>
          <CardDescription>Lønnsinformasjon og kontoopplysninger</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 pt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="hourlyRate">Timelønn (kr) *</Label>
              <Input
                id="hourlyRate"
                type="number"
                value={formData.hourlyRate}
                onChange={(e) => updateFormData({ hourlyRate: Number(e.target.value) })}
                min="0"
                step="0.01"
              />
            </div>
            <div>
              <Label htmlFor="accountNumber">Kontonummer *</Label>
              <Input
                id="accountNumber"
                value={formData.accountNumber}
                onChange={(e) => updateFormData({ accountNumber: e.target.value })}
                placeholder="1234.56.78901"
                className={errors.accountNumber ? "border-red-500" : ""}
              />
              {errors.accountNumber && <p className="text-red-500 text-sm mt-1">{errors.accountNumber}</p>}
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button onClick={handleNext} className="bg-green-500 hover:bg-green-600">
          Neste steg
          <ArrowRight className="h-4 w-4 ml-2" />
        </Button>
      </div>
    </div>
  )
}
