import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ArrowLeft, FileText, <PERSON>er, Eye } from "lucide-react"
import ContractPreview from "@/components/ContractPreview"
import type { FormData } from "@/types/contract"

interface ContractGenerationStepProps {
  formData: FormData
  onPrev: () => void
}

export default function ContractGenerationStep({ formData, onPrev }: ContractGenerationStepProps) {
  const [showPreview, setShowPreview] = useState(false)

  const handlePrint = () => {
    window.print()
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return ""
    const date = new Date(dateString)
    return date.toLocaleDateString("no-NO", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    })
  }

  return (
    <div className="space-y-6">
      {!showPreview ? (
        <>
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center text-base">
                <FileText className="h-5 w-5 mr-2 text-green-500" />
                Sammendrag av kontraktinformasjon
              </CardTitle>
              <CardDescription>Kontroller informasjonen før du genererer kontrakten</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 pt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Ansatt</h4>
                    <div className="space-y-1 text-sm">
                      <p>
                        <span className="font-medium">Navn:</span> {formData.employeeName}
                      </p>
                      <p>
                        <span className="font-medium">Adresse:</span> {formData.employeeAddress}
                      </p>
                      <p>
                        <span className="font-medium">Fødselsdato:</span> {formatDate(formData.employeeBirthDate)}
                      </p>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Stilling</h4>
                    <div className="space-y-1 text-sm">
                      <p>
                        <span className="font-medium">Stillingstittel:</span> {formData.position}
                      </p>
                      <p>
                        <span className="font-medium">Startdato:</span> {formatDate(formData.startDate)}
                      </p>
                      <p>
                        <span className="font-medium">Ansettelsestype:</span>{" "}
                        {formData.employmentType === "fast" ? "Fast ansettelse" : "Midlertidig ansettelse"}
                      </p>
                      <p>
                        <span className="font-medium">Prøvetid:</span>{" "}
                        {formData.probationPeriod ? `Ja (${formData.probationMonths} måneder)` : "Nei"}
                      </p>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Lønn</h4>
                    <div className="space-y-1 text-sm">
                      <p>
                        <span className="font-medium">Timelønn:</span> kr {formData.hourlyRate},-
                      </p>
                      <p>
                        <span className="font-medium">Kontonummer:</span> {formData.accountNumber}
                      </p>
                      <p>
                        <span className="font-medium">Eget verktøy:</span> {formData.ownTools ? "Ja" : "Nei"}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Bedrift</h4>
                    <div className="space-y-1 text-sm">
                      <p>
                        <span className="font-medium">Navn:</span> {formData.companyName}
                      </p>
                      <p>
                        <span className="font-medium">Org.nr:</span> {formData.companyOrgNumber}
                      </p>
                      <p>
                        <span className="font-medium">Adresse:</span> {formData.companyAddress}
                      </p>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Arbeidstid</h4>
                    <div className="space-y-1 text-sm">
                      <p>
                        <span className="font-medium">Timer per uke:</span> {formData.workingHoursPerWeek}
                      </p>
                      <p>
                        <span className="font-medium">Arbeidstid:</span> {formData.workingTime}
                      </p>
                      <p>
                        <span className="font-medium">Overtidstillegg:</span> {formData.overtimeRate}%
                      </p>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Forsikring</h4>
                    <div className="space-y-1 text-sm">
                      <p>
                        <span className="font-medium">Pensjon:</span> {formData.pensionProvider}
                      </p>
                      <p>
                        <span className="font-medium">Forsikring:</span> {formData.insuranceProvider}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-between">
            <Button variant="outline" onClick={onPrev}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Forrige steg
            </Button>
            <div className="space-x-2">
              <Button variant="outline" onClick={() => setShowPreview(true)}>
                <Eye className="h-4 w-4 mr-2" />
                Forhåndsvis kontrakt
              </Button>
              <Button onClick={() => setShowPreview(true)} className="bg-green-500 hover:bg-green-600">
                <FileText className="h-4 w-4 mr-2" />
                Generer kontrakt
              </Button>
            </div>
          </div>
        </>
      ) : (
        <>
          <div className="flex justify-between items-center mb-4">
            <Button variant="outline" onClick={() => setShowPreview(false)}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Tilbake til sammendrag
            </Button>
            <Button onClick={handlePrint} className="bg-green-500 hover:bg-green-600">
              <Printer className="h-4 w-4 mr-2" />
              Skriv ut kontrakt
            </Button>
          </div>
          <ContractPreview formData={formData} />
        </>
      )}
    </div>
  )
}
