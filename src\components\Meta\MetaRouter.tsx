import { Suspense, lazy } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Loading } from '@/ui/Loading';
import MetaErrorBoundary from './MetaErrorBoundary';

// Lazy load meta utility components to prevent them from affecting main bundle
const MetaIndexPage = lazy(() => import('@/pages/meta/MetaIndexPage'));
const LogoPage = lazy(() => import('@/pages/meta/LogoPage'));
const ArbeidskontraktPage = lazy(() => import('@/pages/meta/ArbeidskontraktPage'));

/**
 * Isolated router for Meta Utilities
 * Provides error boundaries and lazy loading to protect the main site
 */
const MetaRouter = () => {
  return (
    <MetaErrorBoundary
      fallbackTitle="Meta Utilities Error"
      fallbackMessage="Det oppstod en feil med meta utilities systemet. Dette påvirker ikke hovednettsiden."
    >
      <Suspense fallback={<Loading />}>
        <Routes>
          {/* Meta utilities index */}
          <Route index element={<MetaIndexPage />} />
          
          {/* Individual meta utilities */}
          <Route 
            path="logo" 
            element={
              <MetaErrorBoundary
                fallbackTitle="Logo Generator Error"
                fallbackMessage="Det oppstod en feil med logo generatoren."
              >
                <LogoPage />
              </MetaErrorBoundary>
            } 
          />
          
          <Route 
            path="arbeidskontrakt" 
            element={
              <MetaErrorBoundary
                fallbackTitle="Arbeidskontrakt Generator Error"
                fallbackMessage="Det oppstod en feil med arbeidskontrakt generatoren."
              >
                <ArbeidskontraktPage />
              </MetaErrorBoundary>
            } 
          />
          
          {/* Catch-all redirect to meta index */}
          <Route path="*" element={<Navigate to="/meta" replace />} />
        </Routes>
      </Suspense>
    </MetaErrorBoundary>
  );
};

export default MetaRouter;
