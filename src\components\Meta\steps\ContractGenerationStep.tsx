import { <PERSON>, Button } from '@/ui';
import { ContractStepProps } from '@/lib/meta/types';
import { ArrowLeft, Download, FileText, CheckCircle } from 'lucide-react';

const ContractGenerationStep = ({ formData, onPrev }: ContractStepProps) => {

  const formatDate = (dateString: string) => {
    if (!dateString) return '__.__.__';
    const date = new Date(dateString);
    return date.toLocaleDateString('no-NO', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const getNamePlaceholder = (value: string) => value || '________________________________';
  const getAddressPlaceholder = (value: string) => value || '________________________________';
  const getPositionPlaceholder = (value: string) => value || '________________________________';
  const getAccountPlaceholder = (value: string) => value || '____.____.__.____';
  const getOrgNumberPlaceholder = (value: string) => value || '___.___.___';
  const getNumberPlaceholder = (value: number, defaultValue?: number) =>
    (value && value > 0) ? value : (defaultValue ? `${defaultValue}` : '____');
  const getTextPlaceholder = (value: string, placeholder = '________________________________') =>
    value || placeholder;

  const generateContract = () => {
    const contractHTML = `
<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arbeidskontrakt - ${formData.employeeName}</title>
    <style>
        @page {
            margin: 2.5cm 2cm;
            size: A4;
        }
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 11pt;
            line-height: 1.5;
            color: #1f2937;
            margin: 0;
            padding: 0;
            background: #ffffff;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 25px;
            border-bottom: 3px solid #1e9545;
            position: relative;
        }
        .header::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #1e9545, #16a34a);
        }
        .company-name {
            font-size: 22pt;
            font-weight: 700;
            color: #1e9545;
            margin-bottom: 8px;
            letter-spacing: -0.025em;
        }
        .company-details {
            font-size: 10pt;
            color: #6b7280;
            margin-bottom: 20px;
            line-height: 1.4;
        }
        .document-title {
            font-size: 18pt;
            font-weight: 700;
            color: #111827;
            margin-top: 20px;
            letter-spacing: 0.05em;
            text-transform: uppercase;
        }
        .section {
            margin-bottom: 25px;
            background: #ffffff;
        }
        .section-title {
            font-size: 13pt;
            font-weight: 600;
            color: #1e9545;
            margin-bottom: 15px;
            padding: 8px 0 8px 0;
            border-bottom: 2px solid #e5e7eb;
            position: relative;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }
        .section-title::before {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 40px;
            height: 2px;
            background: #1e9545;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 15px;
        }
        .info-item {
            margin-bottom: 10px;
            padding: 2px 0;
        }
        .label {
            font-weight: 600;
            color: #374151;
            display: inline-block;
            min-width: 130px;
            margin-right: 8px;
        }
        .value {
            color: #1f2937;
        }
        .signature-section {
            margin-top: 50px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 50px;
            page-break-inside: avoid;
        }
        .signature-box {
            border-top: 2px solid #374151;
            padding-top: 15px;
            text-align: center;
            min-height: 60px;
        }
        .signature-label {
            font-weight: 600;
            color: #374151;
            font-size: 10pt;
        }
        .date-place {
            margin-top: 30px;
            text-align: left;
            font-size: 10pt;
            color: #6b7280;
        }
        @media print {
            body {
                print-color-adjust: exact;
                -webkit-print-color-adjust: exact;
            }
            .section {
                page-break-inside: avoid;
            }
            .signature-section {
                page-break-before: auto;
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">${getNamePlaceholder(formData.companyName)}</div>
        <div class="company-details">
            <div>Org.nr: ${getOrgNumberPlaceholder(formData.companyOrgNumber)}</div>
            <div>${getAddressPlaceholder(formData.companyAddress)}</div>
        </div>
        <div class="document-title">Arbeidskontrakt</div>
    </div>

    <div class="section">
        <div class="section-title">1. PARTENES IDENTITET</div>
        <div class="info-grid">
            <div>
                <div class="info-item"><span class="label">Arbeidsgiver:</span> <span class="value">${getNamePlaceholder(formData.companyName)}</span></div>
                <div class="info-item"><span class="label">Org.nr:</span> <span class="value">${getOrgNumberPlaceholder(formData.companyOrgNumber)}</span></div>
                <div class="info-item"><span class="label">Adresse:</span> <span class="value">${getAddressPlaceholder(formData.companyAddress)}</span></div>
            </div>
            <div>
                <div class="info-item"><span class="label">Arbeidstaker:</span> <span class="value">${getNamePlaceholder(formData.employeeName)}</span></div>
                <div class="info-item"><span class="label">Fødselsdato:</span> <span class="value">${formatDate(formData.employeeBirthDate)}</span></div>
                <div class="info-item"><span class="label">Adresse:</span> <span class="value">${getAddressPlaceholder(formData.employeeAddress)}</span></div>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">2. ARBEIDSSTED OG ARBEIDSOPPGAVER</div>
        <div class="info-item"><span class="label">Arbeidssted:</span> <span class="value">Prosjektbasert innen Ringerike og omegn; oppmøtested avtales for hvert prosjekt</span></div>
        <div class="info-item"><span class="label">Stillingsbetegnelse:</span> <span class="value">${getPositionPlaceholder(formData.position)}</span></div>
        <div class="info-item"><span class="label">Arbeidsoppgaver:</span> <span class="value">Arbeid innen anleggsgartner- og grunnarbeid samt annet arbeid naturlig knyttet til virksomheten</span></div>
    </div>

    <div class="section">
        <div class="section-title">3. ANSETTELSESFORHOLD</div>
        <div class="info-item"><span class="label">Tiltredelsesdato:</span> <span class="value">${formatDate(formData.startDate)}</span></div>
        <div class="info-item"><span class="label">Ansettelsestype:</span> <span class="value">${formData.employmentType === 'fast' ? 'Fast ansettelse' : 'Midlertidig ansettelse'}</span></div>
        ${formData.isTemporary ? `
        <div class="info-item"><span class="label">Sluttdato:</span> <span class="value">${formatDate(formData.temporaryEndDate)}</span></div>
        <div class="info-item"><span class="label">Begrunnelse:</span> <span class="value">${getTextPlaceholder(formData.temporaryReason)}</span></div>
        ` : ''}
        ${formData.probationPeriod ? `
        <div class="info-item"><span class="label">Prøvetid:</span> <span class="value">${getNumberPlaceholder(formData.probationMonths, 6)} måneder med 14 dagers gjensidig oppsigelsesfrist</span></div>
        ` : ''}
    </div>

    <div class="section">
        <div class="section-title">4. ARBEIDSTID OG LØNN</div>
        <div class="info-item"><span class="label">Arbeidstid:</span> <span class="value">${getNumberPlaceholder(formData.workingHoursPerWeek, 37.5)} timer per uke, normalt ${getTextPlaceholder(formData.workingTime, "__.__ - __.__")}</span></div>
        <div class="info-item"><span class="label">Pauser:</span> <span class="value">${getTextPlaceholder(formData.breakTime)}</span></div>
        <div class="info-item"><span class="label">Timelønn:</span> <span class="value">kr ${getNumberPlaceholder(formData.hourlyRate)},-</span></div>
        <div class="info-item"><span class="label">Overtidstillegg:</span> <span class="value">${getNumberPlaceholder(formData.overtimeRate, 40)}% av timelønn</span></div>
        <div class="info-item"><span class="label">Utbetaling:</span> <span class="value">Den ${getNumberPlaceholder(formData.paymentDay, 5)}. hver måned til kontonummer ${getAccountPlaceholder(formData.accountNumber)}</span></div>
        ${formData.ownTools ? `<div class="info-item"><span class="label">Verktøygodtgjørelse:</span> <span class="value">${getTextPlaceholder(formData.toolAllowance)}</span></div>` : ''}
        <div class="info-item"><span class="label">Kjøregodtgjørelse:</span> <span class="value">${getTextPlaceholder(formData.travelAllowance)}</span></div>
    </div>

    <div class="section">
        <div class="section-title">5. FERIE OG PERMISJON</div>
        <div class="info-item"><span class="label">Ferie:</span> <span class="value">5 uker per år i henhold til ferieloven</span></div>
        <div class="info-item"><span class="label">Feriepenger:</span> <span class="value">12% av feriepengegrunnlaget</span></div>
        <div class="info-item"><span class="label">Sykepenger:</span> <span class="value">Arbeidsgiver dekker lønn i arbeidsgiverperioden ved sykdom</span></div>
    </div>

    <div class="section">
        <div class="section-title">6. OPPSIGELSE OG ENDRINGER</div>
        <div class="info-item"><span class="label">Oppsigelsesfrister:</span> <span class="value">${getTextPlaceholder(formData.noticePeriod)}</span></div>
        <div class="info-item"><span class="label">Varslingsregler:</span> <span class="value">${getTextPlaceholder(formData.notificationRules)}</span></div>
        <div class="info-item"><span class="label">Formkrav:</span> <span class="value">Oppsigelse skal være skriftlig</span></div>
    </div>

    <div class="section">
        <div class="section-title">7. PENSJON OG FORSIKRING</div>
        <div class="info-item"><span class="label">Pensjon:</span> <span class="value">${getTextPlaceholder(formData.pensionProvider)} (org.nr ${getOrgNumberPlaceholder(formData.pensionOrgNumber)})</span></div>
        <div class="info-item"><span class="label">Yrkesskadeforsikring:</span> <span class="value">${getTextPlaceholder(formData.insuranceProvider)} (org.nr ${getOrgNumberPlaceholder(formData.insuranceOrgNumber)})</span></div>
    </div>

    <div class="section">
        <div class="section-title">8. ØVRIGE BESTEMMELSER</div>
        <div class="info-item"><span class="label">Tariffavtale:</span> <span class="value">Ingen tariffavtale er gjeldende per dags dato</span></div>
        <div class="info-item"><span class="label">Kompetanseutvikling:</span> <span class="value">Arbeidsgiver tilbyr nødvendig opplæring og vil vurdere kompetanseutviklingstiltak for stillingen</span></div>
    </div>

    <p style="margin-top: 40px; padding: 15px; background: #f9fafb; border-left: 4px solid #1e9545; font-size: 10pt; color: #6b7280; border-radius: 4px;">
        Denne kontrakten er utarbeidet i henhold til Arbeidsmiljøloven § 14-6 og oppfyller alle juridiske krav per ${new Date().toLocaleDateString('no-NO')}.
    </p>

    <div class="date-place">
        <div style="margin-bottom: 20px;">
            <strong>Sted og dato:</strong> Røyse, _______________
        </div>
    </div>

    <div class="signature-section">
        <div class="signature-box">
            <div class="signature-label">Arbeidsgiver</div>
            <div style="margin-top: 10px; font-weight: 600;">${getNamePlaceholder(formData.companyName)}</div>
        </div>
        <div class="signature-box">
            <div class="signature-label">Arbeidstaker</div>
            <div style="margin-top: 10px; font-weight: 600;">${getNamePlaceholder(formData.employeeName)}</div>
        </div>
    </div>
</body>
</html>`;

    return contractHTML;
  };

  const handleDownload = () => {
    const contractHTML = generateContract();
    const blob = new Blob([contractHTML], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `Arbeidskontrakt_${formData.employeeName.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.html`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const handlePrint = () => {
    const contractHTML = generateContract();
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(contractHTML);
      printWindow.document.close();
      printWindow.focus();
      setTimeout(() => {
        printWindow.print();
      }, 250);
    }
  };

  return (
    <div className="space-y-6">
      {/* Summary */}
      <Card title="Sammendrag av kontraktinformasjon" className="mb-6">
        <div className="space-y-4">
          <div className="flex items-center mb-4">
            <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-base sm:text-lg font-medium text-gray-900">Kontrakten er klar for generering</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">Ansatt</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <div><span className="font-medium">Navn:</span> {formData.employeeName}</div>
                <div><span className="font-medium">Stilling:</span> {formData.position}</div>
                <div><span className="font-medium">Startdato:</span> {formatDate(formData.startDate)}</div>
                <div><span className="font-medium">Timelønn:</span> kr {formData.hourlyRate},-</div>
              </div>
            </div>
            
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">Ansettelse</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <div><span className="font-medium">Type:</span> {formData.employmentType === 'fast' ? 'Fast ansettelse' : 'Midlertidig ansettelse'}</div>
                <div><span className="font-medium">Arbeidstid:</span> {formData.workingHoursPerWeek} t/uke</div>
                <div><span className="font-medium">Prøvetid:</span> {formData.probationPeriod ? `${formData.probationMonths} måneder` : 'Nei'}</div>
                <div><span className="font-medium">Eget verktøy:</span> {formData.ownTools ? 'Ja' : 'Nei'}</div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Actions */}
      <Card title="Generer arbeidskontrakt" className="mb-6">
        <div className="space-y-4">
          <div className="flex items-center mb-4">
            <FileText className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-base sm:text-lg font-medium text-gray-900">Last ned eller skriv ut kontrakten</h3>
          </div>
          
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-start">
              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
              <div className="text-sm">
                <p className="text-green-800 font-medium">Kontrakten er juridisk korrekt</p>
                <p className="text-green-700 mt-1">
                  Denne kontrakten oppfyller alle krav i Arbeidsmiljøloven § 14-6 og er klar for signering.
                </p>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              onClick={handleDownload}
              variant="primary"
              size="lg"
              className="flex-1"
            >
              <Download className="h-5 w-5 mr-2" />
              Last ned kontrakt
            </Button>

            <Button
              onClick={handlePrint}
              variant="primary"
              size="lg"
              className="flex-1"
            >
              <FileText className="h-5 w-5 mr-2" />
              Skriv ut
            </Button>
          </div>
        </div>
      </Card>

      {/* Navigation */}
      <div className="flex justify-start">
        <Button
          onClick={onPrev}
          variant="secondary"
          size="lg"
        >
          <ArrowLeft className="h-5 w-5 mr-2" />
          Forrige steg
        </Button>
      </div>
    </div>
  );
};

export default ContractGenerationStep;
