import { <PERSON>, Button } from '@/ui';
import { ContractStepProps } from '@/lib/meta/types';
import { ArrowLeft, Download, FileText, CheckCircle } from 'lucide-react';
// @ts-ignore - html2pdf.js doesn't have types
import html2pdf from 'html2pdf.js';

const ContractGenerationStep = ({ formData, onPrev }: ContractStepProps) => {

  const formatDate = (dateString: string) => {
    if (!dateString) return '__.__.__';
    const date = new Date(dateString);
    return date.toLocaleDateString('no-NO', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const getNamePlaceholder = (value: string) => value || '________________________________';
  const getAddressPlaceholder = (value: string) => value || '________________________________';
  const getOrgNumberPlaceholder = (value: string) => value || '___.___.___';

  const generateContract = () => {
    const contractHTML = `
<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arbeidskontrakt - ${formData.employeeName}</title>
    <style>
        @page {
            margin: 2.5cm 2cm;
            size: A4;
        }
        body {
            font-family: 'Calibri', Arial, sans-serif;
            font-size: 10.5pt;
            line-height: 1.3;
            color: #000;
            margin: 0;
            padding: 0;
            background: #fff;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2pt solid #000;
            padding-bottom: 15px;
            page-break-inside: avoid;
        }
        .company-name {
            font-size: 16pt;
            font-weight: 700;
            color: #000;
            margin-bottom: 4px;
            letter-spacing: 0.2pt;
        }
        .company-details {
            font-size: 9.5pt;
            font-weight: 400;
            color: #333;
            line-height: 1.1;
        }
        .document-title {
            font-size: 14pt;
            font-weight: 700;
            color: #000;
            margin-top: 12px;
            letter-spacing: 0.6pt;
        }
        .section {
            margin-bottom: 14px;
            page-break-inside: avoid;
        }
        .section-title {
            font-size: 11pt;
            font-weight: 700;
            margin-bottom: 6px;
            border-bottom: 1pt solid #411;
            padding-bottom: 3px;
            text-transform: uppercase;
            letter-spacing: 0.15pt;
            page-break-after: avoid;
            color: #000;
        }
        .info-item {
            margin-bottom: 4px;
            line-height: 1.25;
        }
        .label {
            font-weight: 600;
            display: inline-block;
            min-width: 130px;
            vertical-align: top;
            color: #000;
        }
        .value {
            font-weight: 400;
            color: #000;
        }
        .form-field {
            border-bottom: 1pt solid #666;
            display: inline-block;
            min-width: 200px;
            height: 16pt;
            margin-left: 3pt;
        }
        .form-field-short {
            min-width: 80px;
        }
        .form-field-long {
            min-width: 300px;
        }
        .signature-section {
            margin-top: 35px;
            display: table;
            width: 100%;
            page-break-inside: avoid;
        }
        .signature-box {
            display: table-cell;
            width: 50%;
            padding: 0 20px;
            text-align: center;
            vertical-align: top;
        }
        .signature-box:first-child {
            padding-left: 0;
        }
        .signature-box:last-child {
            padding-right: 0;
        }
        .signature-line {
            border-top: 1pt solid #000;
            margin-top: 30px;
            padding-top: 6px;
            font-weight: 600;
            color: #000;
        }
        .signature-date {
            margin-bottom: 12px;
            font-weight: 400;
            color: #555;
        }
        @media print {
            body {
                print-color-adjust: exact;
                -webkit-print-color-adjust: exact;
            }
            .section {
                page-break-inside: avoid;
            }
            .signature-section {
                page-break-before: auto;
                page-break-inside: avoid;
            }
            .legal-notice {
                background: #f8f8f8 !important;
                border: 1px solid #cccccc !important;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">${getNamePlaceholder(formData.companyName).toUpperCase()}</div>
        <div class="company-details">
            <div>Organisasjonsnummer: ${getOrgNumberPlaceholder(formData.companyOrgNumber)}</div>
            <div>${getAddressPlaceholder(formData.companyAddress)}</div>
        </div>
        <div class="document-title">ARBEIDSKONTRAKT</div>
    </div>

    <div class="section">
        <div class="section-title">1. Partenes Identitet</div>
        <div class="info-item"><span class="label">Arbeidsgiver:</span> <span class="value">${getNamePlaceholder(formData.companyName)}</span></div>
        <div class="info-item"><span class="label">Org.nr:</span> <span class="value">${getOrgNumberPlaceholder(formData.companyOrgNumber)}</span></div>
        <div class="info-item"><span class="label">Adresse:</span> <span class="value">${getAddressPlaceholder(formData.companyAddress)}</span></div>
        <div style="margin-top: 8px;">
            <div class="info-item" style="margin-bottom: 2px;"><span class="label">Arbeidstaker:</span> <span class="form-field form-field-long"></span></div>
            <div class="info-item" style="margin-bottom: 2px;"><span class="label">Fødselsdato:</span> <span class="form-field form-field-short"></span></div>
            <div class="info-item" style="margin-bottom: 2px;"><span class="label">Adresse:</span> <span class="form-field form-field-long"></span></div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">2. Arbeidssted og Arbeidsoppgaver</div>
        <div class="info-item"><span class="label">Arbeidssted:</span> <span class="value">Prosjektbasert innen Ringerike og omegn; oppmøtested avtales for hvert prosjekt</span></div>
        <div class="info-item"><span class="label">Stillingsbetegnelse:</span> <span class="form-field form-field-long"></span></div>
        <div class="info-item"><span class="label">Arbeidsoppgaver:</span> <span class="value">Arbeid innen anleggsgartner- og grunnarbeid samt annet arbeid naturlig knyttet til virksomheten</span></div>
    </div>

    <div class="section">
        <div class="section-title">3. Ansettelsesforhold</div>
        <div class="info-item"><span class="label">Tiltredelsesdato:</span> <span class="form-field form-field-short"></span></div>
        <div class="info-item"><span class="label">Ansettelsestype:</span> <span class="value">Fast ansettelse</span></div>
        <div class="info-item"><span class="label">Prøvetid:</span> <span class="value">6 måneder med 14 dagers gjensidig oppsigelsesfrist</span></div>
    </div>

    <div class="section">
        <div class="section-title">4. Arbeidstid og Lønn</div>
        <div class="info-item"><span class="label">Arbeidstid:</span> <span class="value">37.5 timer per uke, normalt 07:00-15:00</span></div>
        <div class="info-item"><span class="label">Pauser:</span> <span class="value">Minst 30 min. ubetalt pause ved arbeidsdag >5,5 t</span></div>
        <div class="info-item"><span class="label">Timelønn:</span> <span class="value">kr 300,-</span></div>
        <div class="info-item"><span class="label">Overtidstillegg:</span> <span class="value">40% av timelønn</span></div>
        <div class="info-item"><span class="label">Utbetaling:</span> <span class="value">Den 5. hver måned til kontonummer</span> <span class="form-field"></span></div>
        <div class="info-item"><span class="label">Kjøregodtgjørelse:</span> <span class="value">Statens gjeldende satser (pt. 3,50 kr/km)</span></div>
    </div>

    <div class="section">
        <div class="section-title">5. Ferie og Permisjon</div>
        <div class="info-item"><span class="label">Ferie:</span> <span class="value">5 uker per år i henhold til ferieloven</span></div>
        <div class="info-item"><span class="label">Feriepenger:</span> <span class="value">12% av feriepengegrunnlaget</span></div>
        <div class="info-item"><span class="label">Sykepenger:</span> <span class="value">Arbeidsgiver dekker lønn i arbeidsgiverperioden ved sykdom</span></div>
    </div>

    <div class="section">
        <div class="section-title">6. Oppsigelse og Endringer</div>
        <div class="info-item"><span class="label">Oppsigelsesfrister:</span> <span class="value">1 måned gjensidig etter prøvetid</span></div>
        <div class="info-item"><span class="label">Varslingsregler:</span> <span class="value">Endringer varsles minimum 2 uker i forveien der mulig</span></div>
        <div class="info-item"><span class="label">Formkrav:</span> <span class="value">Oppsigelse skal være skriftlig</span></div>
    </div>

    <div class="section">
        <div class="section-title">7. Pensjon og Forsikring</div>
        <div class="info-item"><span class="label">Pensjon:</span> <span class="value">Storebrand (org.nr 958 995 369)</span></div>
        <div class="info-item"><span class="label">Yrkesskadeforsikring:</span> <span class="value">Gjensidige Forsikring ASA (org.nr 995 568 217)</span></div>
    </div>

    <div class="section">
        <div class="section-title">8. Øvrige Bestemmelser</div>
        <div class="info-item"><span class="label">Tariffavtale:</span> <span class="value">Ingen tariffavtale er gjeldende per dags dato</span></div>
        <div class="info-item"><span class="label">Kompetanseutvikling:</span> <span class="value">Arbeidsgiver tilbyr nødvendig opplæring og vil vurdere kompetanseutviklingstiltak for stillingen</span></div>
    </div>

    <div class="signature-section">
        <div class="signature-box">
            <div class="signature-date">Dato: <span class="form-field form-field-short"></span></div>
            <div class="signature-line">
                <div>Arbeidsgiver</div>
                <div>Ringerike Landskap AS</div>
            </div>
        </div>
        <div class="signature-box">
            <div class="signature-date">Dato: <span class="form-field form-field-short"></span></div>
            <div class="signature-line">
                <div>Arbeidstaker</div>
                <div><span class="form-field form-field-long"></span></div>
            </div>
        </div>
    </div>
</body>
</html>`;

    return contractHTML;
  };



  const handlePrint = () => {
    const contractHTML = generateContract();
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(contractHTML);
      printWindow.document.close();
      printWindow.focus();
      setTimeout(() => {
        printWindow.print();
      }, 250);
    }
  };

  const handleDownloadPDF = () => {
    const contractHTML = generateContract();

    // Create temporary element for PDF generation
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = contractHTML;
    tempDiv.style.position = 'absolute';
    tempDiv.style.left = '-9999px';
    tempDiv.style.top = '0';
    document.body.appendChild(tempDiv);

    // PDF generation options
    const options = {
      margin: [20, 15, 20, 15], // mm: top, right, bottom, left
      filename: 'arbeidskontrakt.pdf',
      image: { type: 'jpeg', quality: 0.98 },
      html2canvas: {
        scale: 2,
        useCORS: true,
        letterRendering: true
      },
      jsPDF: {
        unit: 'mm',
        format: 'a4',
        orientation: 'portrait'
      }
    };

    // Generate and download PDF
    html2pdf()
      .set(options)
      .from(tempDiv)
      .save()
      .then(() => {
        // Clean up temporary element
        document.body.removeChild(tempDiv);
      })
      .catch((error: any) => {
        console.error('PDF generation failed:', error);
        document.body.removeChild(tempDiv);
        // Fallback to print dialog
        handlePrint();
      });
  };

  return (
    <div className="space-y-6">
      {/* Summary */}
      <Card title="Sammendrag av kontraktinformasjon" className="mb-6">
        <div className="space-y-4">
          <div className="flex items-center mb-4">
            <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-base sm:text-lg font-medium text-gray-900">Kontrakten er klar for generering</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">Ansatt</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <div><span className="font-medium">Navn:</span> {formData.employeeName}</div>
                <div><span className="font-medium">Stilling:</span> {formData.position}</div>
                <div><span className="font-medium">Startdato:</span> {formatDate(formData.startDate)}</div>
                <div><span className="font-medium">Timelønn:</span> kr {formData.hourlyRate},-</div>
              </div>
            </div>
            
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">Ansettelse</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <div><span className="font-medium">Type:</span> {formData.employmentType === 'fast' ? 'Fast ansettelse' : 'Midlertidig ansettelse'}</div>
                <div><span className="font-medium">Arbeidstid:</span> {formData.workingHoursPerWeek} t/uke</div>
                <div><span className="font-medium">Prøvetid:</span> {formData.probationPeriod ? `${formData.probationMonths} måneder` : 'Nei'}</div>
                <div><span className="font-medium">Eget verktøy:</span> {formData.ownTools ? 'Ja' : 'Nei'}</div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Actions */}
      <Card title="Generer arbeidskontrakt" className="mb-6">
        <div className="space-y-4">
          <div className="flex items-center mb-4">
            <FileText className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-base sm:text-lg font-medium text-gray-900">Last ned eller skriv ut kontrakten</h3>
          </div>
          
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-start">
              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
              <div className="text-sm">
                <p className="text-green-800 font-medium">Kontrakten er juridisk korrekt</p>
                <p className="text-green-700 mt-1">
                  Denne kontrakten oppfyller alle krav i Arbeidsmiljøloven § 14-6 og er klar for signering.
                </p>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              onClick={handleDownloadPDF}
              variant="primary"
              size="lg"
              className="flex-1"
            >
              <Download className="h-5 w-5 mr-2" />
              Last ned PDF
            </Button>

            <Button
              onClick={handlePrint}
              variant="primary"
              size="lg"
              className="flex-1"
            >
              <FileText className="h-5 w-5 mr-2" />
              Skriv ut
            </Button>
          </div>
        </div>
      </Card>

      {/* Navigation */}
      <div className="flex justify-start">
        <Button
          onClick={onPrev}
          variant="secondary"
          size="lg"
        >
          <ArrowLeft className="h-5 w-5 mr-2" />
          Forrige steg
        </Button>
      </div>
    </div>
  );
};

export default ContractGenerationStep;
