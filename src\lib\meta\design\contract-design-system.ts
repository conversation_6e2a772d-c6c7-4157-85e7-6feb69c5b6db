/**
 * Semantic Design System for Arbeidskontrakt PDF Generator
 *
 * This design system is structured to match the actual arbeidskontrakt sections,
 * making it intuitive to customize each part of the contract independently.
 *
 * Design Philosophy:
 * - Semantic naming: Each style maps directly to contract elements
 * - Section-based organization: Mirrors the actual contract structure
 * - Easy customization: Modify specific contract sections without hunting
 * - Maintainability: Clear relationship between design and content
 */

// ============================================================================
// FOUNDATION TOKENS - Core design variables
// ============================================================================

export const foundationTokens = {
  // Brand Identity
  brand: {
    primary: '#000000',      // Main brand color (black for professional look)
    secondary: '#374151',    // Supporting gray
    accent: '#dbdbdb',     // Light borders and dividers
  },

  // Color Palette
  colors: {
    text: {
      primary: '#000000',    // Main text (black)
      secondary: '#374151',  // Secondary text (dark gray)
      muted: '#6b7280',      // Muted text (light gray)
    },
    background: {
      page: '#ffffff',       // Page background
      section: '#ffffff',    // Section background
    },
    borders: {
      light: '#e5e7eb',      // Light borders
      medium: '#9ca3af',     // Medium borders
      dark: '#374151',       // Dark borders
    },
  },

  // Typography Foundation
  typography: {
    // Font family for professional contract documents (React-PDF built-in)
    fontFamily: 'Helvetica',

    // Font weights - mapped to what built-in fonts actually support
    weights: {
      light: 'normal',     // Use normal (built-in fonts have limited weights)
      normal: 'normal',    // Helvetica normal
      medium: 'normal',    // Use normal
      semiBold: 'bold',    // Use bold
      bold: 'bold',        // Helvetica bold
      extraBold: 'bold',   // Use bold
      black: 'bold',       // Use bold
    },

    // Line heights for different content types
    lineHeights: {
      tight: 1.2,          // For titles
      normal: 1.4,         // For body text
      relaxed: 1.6,        // For legal text
    },
  },

  // Spacing Foundation
  spacing: {
    // Micro spacing
    micro: 1,
    tiny: 2,
    small: 4,

    // Standard spacing
    base: 8,
    medium: 12,
    large: 16,

    // Macro spacing
    section: 20,
    page: 30,
    major: 40,
  },

  // Layout Foundation
  layout: {
    page: {
      margin: 30,
      width: 'A4',
    },
    columns: {
      gap: 20,
      width: '45%',
    },
    borders: {
      thin: 1,
      medium: 1.5,
      thick: 2,
    },
  },
} as const;

// ============================================================================
// ARBEIDSKONTRAKT SEMANTIC DESIGN SECTIONS
// ============================================================================

/**
 * PAGE FOUNDATION
 * Overall document structure and base styling
 */
export const pageDesign = {
  document: {
    flexDirection: 'column' as const,
    backgroundColor: foundationTokens.colors.background.page,
    padding: foundationTokens.layout.page.margin,
    fontFamily: foundationTokens.typography.fontFamily,
    fontSize: 11, // Base reading size
    lineHeight: foundationTokens.typography.lineHeights.normal,
    color: foundationTokens.colors.text.primary,
  },
} as const;

/**
 * CONTRACT HEADER
 * Company branding, organization details, and main title
 */
export const contractHeaderDesign = {
  container: {                    // Header wrapper with bottom border
    textAlign: 'center' as const,
    marginBottom: foundationTokens.spacing.major,
    paddingBottom: foundationTokens.spacing.large,
    borderBottomWidth: foundationTokens.layout.borders.thick,
    borderBottomColor: foundationTokens.brand.primary,
  },

  companyName: {                  // "Ringerike Landskap AS"
    fontFamily: foundationTokens.typography.fontFamily,
    fontSize: 18,
    fontWeight: foundationTokens.typography.weights.bold,
    color: foundationTokens.brand.primary,
    marginBottom: foundationTokens.spacing.small,
    letterSpacing: 0.5,
  },

  organizationInfo: {             // "Org.nr: ***********" and address
    fontFamily: foundationTokens.typography.fontFamily,
    fontSize: 11,
    fontWeight: foundationTokens.typography.weights.normal,
    color: foundationTokens.colors.text.secondary,
    marginBottom: foundationTokens.spacing.tiny,
  },

  contractTitle: {                // "ARBEIDSKONTRAKT"
    fontSize: 16,
    // fontWeight: foundationTokens.typography.weights.bold,
    fontWeight: foundationTokens.typography.weights.extraBold,
    color: foundationTokens.colors.text.primary,
    marginTop: foundationTokens.spacing.large,
    letterSpacing: 1,
  },
} as const;

/**
 * SECTION 1: PARTENES IDENTITET (Party Identity)
 * Employer and employee information in two-column layout
 */
export const partyIdentityDesign = {
  container: {                    // Section 1 wrapper
    marginBottom: foundationTokens.spacing.section,
    breakInside: 'avoid' as const,
  },

  sectionTitle: {                 // "1. PARTENES IDENTITET"
    fontSize: 13,
    fontWeight: foundationTokens.typography.weights.bold,
    color: foundationTokens.brand.primary,
    marginBottom: foundationTokens.spacing.medium,
    paddingBottom: foundationTokens.spacing.small,
    borderBottomWidth: foundationTokens.layout.borders.thin,
    borderBottomColor: foundationTokens.brand.accent,
    breakAfter: 'avoid' as const,
  },

  twoColumnRow: {                 // Employer/Employee side-by-side layout
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    marginBottom: foundationTokens.spacing.large,
    breakInside: 'avoid' as const,
  },

  employerColumn: {               // Left column: Arbeidsgiver info
    flex: 1,
    paddingRight: foundationTokens.layout.columns.gap,
  },

  employeeColumn: {               // Right column: Arbeidstaker info
    flex: 1,
    paddingLeft: foundationTokens.spacing.small,
  },

  fieldLabel: {                   // "Arbeidsgiver:", "Org.nr:", "Adresse:", etc.
    fontWeight: foundationTokens.typography.weights.bold,
    fontSize: 11,
    marginBottom: foundationTokens.spacing.tiny,
    color: foundationTokens.colors.text.primary,
  },

  fieldValue: {                   // Company name, employee name, addresses, etc.
    fontSize: 11,
    marginBottom: foundationTokens.spacing.base,
    color: foundationTokens.colors.text.primary,
    lineHeight: foundationTokens.typography.lineHeights.normal,
  },
} as const;

/**
 * SECTION 2: ARBEIDSSTED OG ARBEIDSOPPGAVER (Work Location and Tasks)
 * Job description and workplace information
 */
export const workLocationTasksDesign = {
  container: {                    // Section 2 wrapper
    marginBottom: foundationTokens.spacing.section,
    breakInside: 'avoid' as const,
  },

  sectionTitle: {                 // "2. ARBEIDSSTED OG ARBEIDSOPPGAVER"
    fontSize: 13,
    fontWeight: foundationTokens.typography.weights.bold,
    color: foundationTokens.brand.primary,
    marginBottom: foundationTokens.spacing.medium,
    paddingBottom: foundationTokens.spacing.small,
    borderBottomWidth: foundationTokens.layout.borders.thin,
    borderBottomColor: foundationTokens.brand.accent,
  },

  fieldLabel: {                   // "Arbeidssted:", "Stillingsbetegnelse:", "Arbeidsoppgaver:"
    fontWeight: foundationTokens.typography.weights.bold,
    fontSize: 11,
    marginBottom: foundationTokens.spacing.tiny,
    color: foundationTokens.colors.text.primary,
  },

  fieldValue: {                   // Job location, position title, job description
    fontSize: 11,
    marginBottom: foundationTokens.spacing.base,
    color: foundationTokens.colors.text.primary,
    lineHeight: foundationTokens.typography.lineHeights.normal,
  },
} as const;

/**
 * SECTION 3: ANSETTELSESFORHOLD (Employment Terms)
 * Employment type, start date, probation period
 */
export const employmentTermsDesign = {
  container: {                    // Section 3 wrapper
    marginBottom: foundationTokens.spacing.section,
    breakInside: 'avoid' as const,
  },

  sectionTitle: {                 // "3. ANSETTELSESFORHOLD"
    fontSize: 13,
    fontWeight: foundationTokens.typography.weights.bold,
    color: foundationTokens.brand.primary,
    marginBottom: foundationTokens.spacing.medium,
    paddingBottom: foundationTokens.spacing.small,
    borderBottomWidth: foundationTokens.layout.borders.thin,
    borderBottomColor: foundationTokens.brand.accent,
  },

  inlineLabel: {                  // "Tiltredelsesdato:", "Ansettelsestype:", "Prøvetid:"
    fontWeight: foundationTokens.typography.weights.bold,
    fontSize: 11,
    color: foundationTokens.colors.text.primary,
  },

  inlineValue: {                  // Start date, employment type, probation period text
    fontSize: 11,
    marginBottom: foundationTokens.spacing.base,
    color: foundationTokens.colors.text.primary,
    lineHeight: foundationTokens.typography.lineHeights.normal,
  },
} as const;

/**
 * SECTION 4: ARBEIDSTID OG LØNN (Work Time and Salary)
 * Working hours, salary, overtime, payment details
 */
export const workTimeSalaryDesign = {
  container: {                    // Section 4 wrapper
    marginBottom: foundationTokens.spacing.section,
    breakInside: 'avoid' as const,
  },

  sectionTitle: {                 // "4. ARBEIDSTID OG LØNN"
    fontSize: 13,
    fontWeight: foundationTokens.typography.weights.bold,
    color: foundationTokens.brand.primary,
    marginBottom: foundationTokens.spacing.medium,
    paddingBottom: foundationTokens.spacing.small,
    borderBottomWidth: foundationTokens.layout.borders.thin,
    borderBottomColor: foundationTokens.brand.accent,
  },

  inlineLabel: {                  // "Arbeidstid:", "Pauser:", "Timelønn:", "Overtidstillegg:", etc.
    fontWeight: foundationTokens.typography.weights.bold,
    fontSize: 11,
    color: foundationTokens.colors.text.primary,
  },

  inlineValue: {                  // Work hours, break time, hourly rate, overtime rate, etc.
    fontSize: 11,
    marginBottom: foundationTokens.spacing.base,
    color: foundationTokens.colors.text.primary,
    lineHeight: foundationTokens.typography.lineHeights.normal,
  },
} as const;

/**
 * SECTIONS 5-8: STANDARD CONTRACT SECTIONS
 * Vacation, termination, pension, other terms
 */
export const standardSectionsDesign = {
  container: {                    // Sections 5-8 wrapper
    marginBottom: foundationTokens.spacing.section,
    breakInside: 'avoid' as const,
  },

  sectionTitle: {                 // "5. FERIE OG PERMISJON", "6. OPPSIGELSE OG ENDRINGER", etc.
    fontSize: 13,
    fontWeight: foundationTokens.typography.weights.bold,
    color: foundationTokens.brand.primary,
    marginBottom: foundationTokens.spacing.medium,
    paddingBottom: foundationTokens.spacing.small,
    borderBottomWidth: foundationTokens.layout.borders.thin,
    borderBottomColor: foundationTokens.brand.accent,
  },

  inlineLabel: {                  // "Ferie:", "Pensjon:", "Oppsigelsesfrister:", etc.
    fontWeight: foundationTokens.typography.weights.bold,
    fontSize: 11,
    color: foundationTokens.colors.text.primary,
  },

  inlineValue: {                  // Vacation days, pension provider, notice periods, etc.
    fontSize: 11,
    marginBottom: foundationTokens.spacing.base,
    color: foundationTokens.colors.text.primary,
    lineHeight: foundationTokens.typography.lineHeights.normal,
  },

  keepTogether: {                 // Prevent section breaks
    breakInside: 'avoid' as const,
    orphans: 3,
    widows: 3,
  },
} as const;

/**
 * LEGAL REFERENCE TEXT
 * Small print, disclaimers, and legal compliance text
 */
export const legalReferenceDesign = {
  text: {                         // "Denne kontrakten er utarbeidet i henhold til Arbeidsmiljøloven..."
    fontSize: 9,
    fontWeight: foundationTokens.typography.weights.normal,
    color: foundationTokens.colors.text.muted,
    marginBottom: foundationTokens.spacing.section,
    lineHeight: foundationTokens.typography.lineHeights.relaxed,
    textAlign: 'center' as const,
  },
} as const;

/**
 * CONTRACT SIGNATURE SECTION
 * Signature areas for employer and employee
 */
export const contractSignatureDesign = {
  container: {                    // Signature section wrapper with top border
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    marginTop: foundationTokens.spacing.major,
    paddingTop: foundationTokens.spacing.section,
    borderTopWidth: foundationTokens.layout.borders.thin,
    borderTopColor: foundationTokens.brand.accent,
    breakInside: 'avoid' as const,
  },

  signatureBox: {                 // Individual signature area (employer/employee)
    width: foundationTokens.layout.columns.width,
    textAlign: 'center' as const,
  },

  signatureLine: {                // Line where signature is written
    borderTopWidth: foundationTokens.layout.borders.thin,
    borderTopColor: foundationTokens.colors.borders.medium,
    paddingTop: foundationTokens.spacing.base,
    marginBottom: foundationTokens.spacing.medium,
    height: 40, // Space for actual signature
  },

  roleLabel: {                    // "Arbeidsgiver", "Arbeidstaker"
    fontWeight: foundationTokens.typography.weights.bold,
    fontSize: 11,
    marginBottom: foundationTokens.spacing.tiny,
    color: foundationTokens.colors.text.primary,
  },

  nameText: {                     // Company name, employee name
    fontSize: 11,
    color: foundationTokens.colors.text.primary,
    marginBottom: foundationTokens.spacing.tiny,
  },

  dateText: {                     // "Dato: [date]"
    fontSize: 11,
    color: foundationTokens.colors.text.secondary,
  },
} as const;

/**
 * PAGE FOOTER
 * Page numbering and document metadata
 */
export const pageFooterDesign = {
  pageNumber: {                   // "1 / 2" page numbers at bottom right
    position: 'absolute' as const,
    bottom: 15,
    right: 30,
    fontSize: 9,
    color: foundationTokens.colors.text.muted,
    fontWeight: foundationTokens.typography.weights.normal,
  },
} as const;

// ============================================================================
// SEMANTIC ARBEIDSKONTRAKT DESIGN SYSTEM
// ============================================================================

/**
 * Complete semantic design system for arbeidskontrakt
 * Each section maps directly to contract structure for intuitive customization
 */
export const contractDesignSystem = {
  // Foundation
  foundation: foundationTokens,

  // Document structure
  page: pageDesign,

  // Contract sections (semantic naming)
  contractHeader: contractHeaderDesign,
  partyIdentity: partyIdentityDesign,           // Section 1
  workLocationTasks: workLocationTasksDesign,   // Section 2
  employmentTerms: employmentTermsDesign,       // Section 3
  workTimeSalary: workTimeSalaryDesign,         // Section 4
  standardSections: standardSectionsDesign,     // Sections 5-8
  legalReference: legalReferenceDesign,
  contractSignature: contractSignatureDesign,
  pageFooter: pageFooterDesign,

  // Legacy compatibility (for gradual migration)
  header: contractHeaderDesign,
  content: standardSectionsDesign,
  legal: legalReferenceDesign,
  signature: contractSignatureDesign,
  footer: pageFooterDesign,
} as const;

// Type for design system
export type ContractDesignSystem = typeof contractDesignSystem;
