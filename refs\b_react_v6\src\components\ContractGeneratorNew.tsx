import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { CheckCircle, Circle } from 'lucide-react';
import BasicInfoStep from '@/components/BasicInfoStep';
import AdvancedSettingsStep from '@/components/AdvancedSettingsStep';
import ContractGenerationStep from '@/components/ContractGenerationStep';
import Header from '@/components/Header';
import type { FormData } from '@/types/contract';

const initialFormData: FormData = {
  // Basic Info
  employeeName: "",
  employeeAddress: "",
  employeeBirthDate: "",
  startDate: "",
  position: "",
  hourlyRate: 300,
  accountNumber: "",
  employmentType: "fast",
  isTemporary: false,
  temporaryEndDate: "",
  temporaryReason: "",
  probationPeriod: true,
  probationMonths: 6,
  ownTools: false,

  // Advanced Settings
  companyName: "Ringerike Landskap AS",
  companyOrgNumber: "***********",
  companyAddress: "Birchs vei 7, 3530 Røyse",
  workingHoursPerWeek: 37.5,
  workingTime: "07:00-15:00",
  breakTime: "Minst 30 min. ubetalt pause ved arbeidsdag >5,5 t",
  overtimeRate: 40,
  paymentDay: 5,
  toolAllowance: "kr 1,85 per time ved bruk av eget håndverktøy",
  travelAllowance: "Statens gjeldende satser (pt. 3,50 kr/km)",
  pensionProvider: "Storebrand",
  pensionOrgNumber: "***********",
  insuranceProvider: "Gjensidige Forsikring ASA",
  insuranceOrgNumber: "***********",
  noticePeriod: "1 måned gjensidig etter prøvetid",
  contractDuration: "",
  notificationRules: "Endringer varsles minimum 2 uker i forveien der mulig",
};

const ContractGenerator = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<FormData>(initialFormData);

  const steps = [
    { number: 1, title: "Grunnleggende informasjon", description: "Personopplysninger og stillingsinformasjon" },
    { number: 2, title: "Kontraktdetaljer", description: "Bedriftsinformasjon og juridiske bestemmelser" },
    { number: 3, title: "Kontraktgenerering", description: "Sammendrag og generering av kontrakt" },
  ];

  const updateFormData = (data: Partial<FormData>) => {
    setFormData((prev) => ({
      ...prev,
      ...data,
      // Update employmentType based on isTemporary
      employmentType:
        data.isTemporary !== undefined ? (data.isTemporary ? "midlertidig" : "fast") : prev.employmentType,
    }));
  };

  const nextStep = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const goToStep = (step: number) => {
    setCurrentStep(step);
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return <BasicInfoStep formData={formData} updateFormData={updateFormData} onNext={nextStep} />;
      case 2:
        return (
          <AdvancedSettingsStep
            formData={formData}
            updateFormData={updateFormData}
            onNext={nextStep}
            onPrev={prevStep}
          />
        );
      case 3:
        return <ContractGenerationStep formData={formData} onPrev={prevStep} />;
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-green-50 to-lime-50 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <Header />

        {/* Progress Indicator */}
        <Card className="mb-6">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between mb-4">
              <CardTitle className="text-lg">Fremdrift</CardTitle>
              <span className="text-sm text-gray-500">Steg {currentStep} av 3</span>
            </div>
            <Progress value={(currentStep / 3) * 100} className="mb-4" />
          </CardHeader>
          <CardContent className="pt-0">
            <div className="flex justify-between">
              {steps.map((step) => (
                <div key={step.number} className="flex flex-col items-center flex-1">
                  <Button
                    variant="ghost"
                    className={`w-12 h-12 rounded-full p-0 mb-2 ${
                      currentStep === step.number
                        ? "bg-green-500 text-white hover:bg-green-600"
                        : currentStep > step.number
                          ? "bg-green-100 text-green-600 hover:bg-green-200"
                          : "bg-gray-100 text-gray-400"
                    }`}
                    onClick={() => goToStep(step.number)}
                  >
                    {currentStep > step.number ? <CheckCircle className="h-6 w-6" /> : <Circle className="h-6 w-6" />}
                  </Button>
                  <div className="text-center">
                    <p
                      className={`font-medium text-sm ${
                        currentStep >= step.number ? "text-gray-900" : "text-gray-500"
                      }`}
                    >
                      {step.title}
                    </p>
                    <p className="text-xs text-gray-500 mt-1 max-w-32">{step.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Step Content */}
        {renderStep()}
      </div>
    </div>
  );
};

export default ContractGenerator;
